import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.animation import Func<PERSON><PERSON><PERSON>
from matplotlib.ticker import <PERSON><PERSON><PERSON><PERSON>att<PERSON>
from itertools import cycle
import textwrap
import tempfile
from pathlib import Path
from typing import Optional
from src.logger import get_logger
from src.animation._easing_functions import EASING_FUNCTIONS
from src.music import MusicManager

LOGGER = get_logger(__name__)


def create_wealth_animation(
    wealth_df: pd.DataFrame,
    investment_years: float | int,
    filename: str = "wealth_animation.mp4",
    duration_sec: int = 8,
    fps: int = 30,
    title: str = "Wealth Projection Over Time",
    easing_function: str = None,
    music_filename: Optional[str] = None,
    music_dir: Optional[Path] = None,
):
    """Create a stylish wealth animation with glowing lines and non-linear pacing.

    Generates an animated visualization of wealth accumulation over time for
    multiple investments. The animation features glowing line effects, smooth
    easing transitions, and a summary table that fades in at the end showing
    final performance metrics. Optionally includes background music.

    Args:
        wealth_df (pd.DataFrame): DataFrame containing wealth data over time.
            Must include a "Total Investments" column and one or more columns
            for individual investment wealth projections.
        investment_years (float | int): Number of years covered by the investment
            period, used for calculating annualized returns.
        filename (str, optional): Output filename for the animation video.
            Defaults to "wealth_animation.mp4".
        duration_sec (int, optional): Duration of the main animation in seconds,
            excluding the summary fade-in period. Defaults to 8.
        fps (int, optional): Frames per second for the animation. Higher values
            create smoother animations but larger file sizes. Defaults to 30.
        title (str, optional): Title displayed at the top of the animation.
            Defaults to "Wealth Projection Over Time".
        easing_function (str, optional): Name of the easing function to apply
            for non-linear animation pacing. Must be a key in EASING_FUNCTIONS
            dictionary. If None, linear pacing is used. Defaults to None.
        music_filename (Optional[str], optional): Specific music file to use for
            background audio. If None, the first available music file is used.
            If no music files are available, no audio is added. Defaults to None.
        music_dir (Optional[Path], optional): Path to the music directory containing
            music files and creators.toml configuration. If None, defaults to
            'assets/music' relative to project root. Defaults to None.

    Returns:
        None: The function saves the animation to the specified filename and
            logs the creation process. If music is available, creates a version
            with background audio.

    Note:
        The animation includes a 2-second summary fade-in period after the main
        animation, showing final rankings, wealth values, CAGR, and volatility
        for each investment. Music integration requires FFmpeg and properly
        configured music files in the assets/music directory.
    """
    LOGGER.activate()

    # --- Data and Animation Setup ---
    investment_col_name = "Total Investments"
    investment_series = wealth_df[investment_col_name]
    stock_wealth_df = wealth_df.drop(columns=[investment_col_name])

    total_investment = investment_series.iloc[-1]

    first_investment_val = investment_series[investment_series > 0].iloc[0]

    SUMMARY_FADE_IN_SEC = 2
    main_animation_frames = duration_sec * fps
    summary_frames = SUMMARY_FADE_IN_SEC * fps
    total_animation_frames = main_animation_frames + summary_frames

    easing_function = EASING_FUNCTIONS.get(easing_function, lambda x: x)
    normalized_time = np.linspace(0, 1, num=main_animation_frames)
    eased_time = np.array([easing_function(t) for t in normalized_time])
    main_frame_indices = (eased_time * (len(wealth_df) - 1)).astype(int)

    # --- Plot Setup ---
    plt.style.use("seaborn-v0_8-darkgrid")
    fig, ax = plt.subplots(figsize=(12, 16), dpi=300)
    fig.patch.set_facecolor("#0a0a0a")
    ax.set_facecolor("#0a0a0a")

    # More vibrant and appealing colors with better contrast
    VIBRANT_COLORS = [
        "#00D4FF",  # Electric blue
        "#FF6B35",  # Vibrant orange
        "#32FF32",  # Bright green
        "#FF3366",  # Hot pink
        "#9D4EDD",  # Purple
        "#FFD23F",  # Golden yellow
        "#06FFA5",  # Mint green
        "#FF8500",  # Orange
        "#B7E4C7",  # Light green
        "#FF006E",  # Magenta
    ]
    color_map = {
        ticker: color
        for ticker, color in zip(stock_wealth_df.columns, cycle(VIBRANT_COLORS))
    }

    # --- Create line groups for enhanced glow effect ---
    stock_line_groups = {}
    for ticker in stock_wealth_df.columns:
        color = color_map[ticker]
        # Create a more pronounced "glow" effect with multiple layers
        glow_lines = [
            ax.plot([], [], color=color, lw=15, alpha=0.08, zorder=1)[0],  # Outer glow
            ax.plot([], [], color=color, lw=10, alpha=0.12, zorder=2)[0],  # Mid glow
            ax.plot([], [], color=color, lw=6, alpha=0.20, zorder=3)[0],   # Inner glow
            ax.plot([], [], color=color, lw=4, alpha=1.0, zorder=4, label=ticker)[0],  # Main line
        ]
        stock_line_groups[ticker] = glow_lines

    # Make investment line more subtle but visible
    investment_line = ax.plot(
        [], [], lw=3, ls="--", color="#888888", alpha=0.8, label=investment_col_name
    )[0]

    # Flatten the list of all lines for the animation manager
    all_stock_lines = [line for group in stock_line_groups.values() for line in group]
    all_lines = all_stock_lines + [investment_line]

    # Move timestamp to lower right corner with better styling
    date_text = ax.text(
        0.98,
        0.02,
        "",
        transform=ax.transAxes,
        fontsize=28,
        color="#00D4FF",
        ha="right",
        va="bottom",
        fontname="monospace",
        weight="bold",
        bbox=dict(boxstyle="round,pad=0.3", facecolor="#0a0a0a", edgecolor="#00D4FF", alpha=0.8)
    )

    # --- Enhanced Styling ---
    wrapped_title = "\n".join(textwrap.wrap(title, width=45))
    ax.set_title(wrapped_title, fontsize=32, color="#00D4FF", weight="bold", pad=30)

    # Remove x-axis for cleaner look (timestamp shows date instead)
    ax.set_xlabel("")
    ax.tick_params(axis="x", which="both", bottom=False, labelbottom=False)

    # Enhanced y-axis styling
    ax.set_ylabel("Portfolio Value", fontsize=24, color="white", labelpad=20, weight="bold")
    ax.tick_params(axis="y", colors="white", labelsize=16, width=2, length=8)
    ax.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f"${x:,.0f}"))

    # Enhanced legend with better positioning and styling
    ax.legend(
        loc="upper left",
        bbox_to_anchor=(0.02, 0.95),
        fontsize=16,
        frameon=True,
        facecolor="#0a0a0a",
        edgecolor="#00D4FF",
        labelcolor="white",
        framealpha=0.9,
        borderpad=1,
        columnspacing=1,
        handlelength=2,
        handletextpad=0.8
    )

    # Subtle grid for better readability
    ax.grid(True, alpha=0.15, color="white", linewidth=0.5)
    ax.set_axisbelow(True)

    # Adjust layout with more padding
    plt.tight_layout(pad=3)

    # --- Final Summary Data ---
    final_wealth = stock_wealth_df.iloc[-1]
    cagr = (final_wealth / total_investment) ** (1 / investment_years) - 1
    cagr_sorted = cagr.sort_values(ascending=False)

    asset_returns = stock_wealth_df.pct_change()
    # Annualize the standard deviation of monthly returns
    volatility = asset_returns.std()

    # Enhanced summary text with better styling
    summary_text_obj = ax.text(
        0.5,
        0.5,
        "",
        transform=ax.transAxes,
        fontsize=16,
        color="white",
        ha="center",
        va="center",
        fontname="monospace",
        alpha=0,
        weight="bold",
        bbox=dict(
            boxstyle="round,pad=1.0",
            facecolor="#0a0a0a",
            edgecolor="#00D4FF",
            alpha=0,
            linewidth=2
        ),
    )
    summary_artists = [summary_text_obj]

    def update(frame_num):
        if frame_num < main_animation_frames:
            data_idx = main_frame_indices[frame_num]
            current_data = wealth_df.iloc[: data_idx + 1]

            # --- MODIFIED: Update all lines in each glow group ---
            for ticker, line_group in stock_line_groups.items():
                for line in line_group:
                    line.set_data(current_data.index, current_data[ticker])
            investment_line.set_data(
                current_data.index, current_data[investment_col_name]
            )

            # Enhanced axis limits with better padding
            y_max = current_data.iloc[:, :-1].max().max() * 1.15
            current_min_wealth = stock_wealth_df.iloc[: data_idx + 1].min().min()
            y_min = min(first_investment_val, current_min_wealth) * 0.90

            ax.set_xlim(
                wealth_df.index.min(), current_data.index.max() + pd.Timedelta(days=60)
            )
            ax.set_ylim(y_min, y_max if y_max > y_min else y_min + 1)

            # Enhanced date formatting with better styling
            current_date = current_data.index.max().strftime("%b %Y")
            date_text.set_text(current_date)
        else:
            if frame_num == main_animation_frames:
                # Calculate dynamic column widths based on actual content
                rank_width = 6
                ticker_width = max(
                    8, max(len(ticker) for ticker in cagr_sorted.index) + 2
                )
                wealth_width = max(
                    12,
                    max(
                        len(f"${final_wealth[ticker]:,.0f}")
                        for ticker in cagr_sorted.index
                    )
                    + 2,
                )
                cagr_width = 8
                volatility_width = 12

                # Create enhanced header with better formatting
                header = (
                    f"{'🏆 Rank':<{rank_width}}"
                    f"{'📈 Ticker':<{ticker_width}}"
                    f"{'💰 Final Wealth':>{wealth_width}}"
                    f"{'📊 CAGR':>{cagr_width}}"
                    f"{'⚡ Risk':>{volatility_width}}"
                )
                separator = "═" * len(header)

                # Create enhanced body lines with ranking emojis
                body_lines = []
                rank_emojis = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣", "6️⃣", "7️⃣", "8️⃣", "9️⃣", "🔟"]

                for i, (ticker, cagr_val) in enumerate(cagr_sorted.items()):
                    rank_emoji = rank_emojis[i] if i < len(rank_emojis) else f"{i+1}️⃣"
                    rank_str = f"{rank_emoji}"
                    wealth_str = f"${final_wealth[ticker]:,.0f}"
                    cagr_str = f"{cagr_val:.1%}"
                    volatility_str = f"{volatility[ticker]:.1%}"

                    line = (
                        f"{rank_str:<{rank_width}}"
                        f"{ticker:<{ticker_width}}"
                        f"{wealth_str:>{wealth_width}}"
                        f"{cagr_str:>{cagr_width}}"
                        f"{volatility_str:>{volatility_width}}"
                    )
                    body_lines.append(line)

                body = "\n".join(body_lines)

                # Create enhanced footer with investment summary
                footer_separator = "─" * len(header)
                footer_string = f"💵 Total Invested: ${total_investment:,.0f}"

                # Calculate total final wealth for additional context
                total_final_wealth = final_wealth.sum()
                total_return_str = f"🏦 Portfolio Value: ${total_final_wealth:,.0f}"
                overall_return_pct = ((total_final_wealth / total_investment) - 1) * 100
                return_emoji = "🚀" if overall_return_pct > 0 else "📉"
                overall_return_str = f"{return_emoji} Overall Return: {overall_return_pct:+.1f}%"

                summary_string = (
                    f"🎯 INVESTMENT PERFORMANCE SUMMARY\n"
                    f"{separator}\n"
                    f"{header}\n"
                    f"{separator}\n"
                    f"{body}\n"
                    f"{footer_separator}\n"
                    f"{footer_string}\n"
                    f"{total_return_str}\n"
                    f"{overall_return_str}"
                )

                summary_text_obj.set_text(summary_string)

            # Enhanced fade-in effect with smooth easing
            fade_in_progress = (frame_num - main_animation_frames) / summary_frames
            # Use ease-out cubic for smoother fade-in
            alpha_value = min(1.0, 1 - (1 - fade_in_progress) ** 3)

            summary_text_obj.set_alpha(alpha_value)
            summary_text_obj.get_bbox_patch().set_alpha(min(0.95, alpha_value * 0.95))

        return all_lines + [date_text] + summary_artists

    # --- Create and Save ---
    LOGGER.info(f"Creating {duration_sec + SUMMARY_FADE_IN_SEC}-second animation ...")
    ani = FuncAnimation(
        fig,
        update,
        frames=total_animation_frames,
        init_func=lambda: all_lines + [date_text] + summary_artists,
        blit=False,
    )

    # Initialize music manager
    music_manager = MusicManager(music_dir)

    # Determine final output filename
    final_filename = filename
    temp_video_path = None

    # If music is available, save to temporary file first
    if music_manager.has_music():
        temp_video_path = Path(tempfile.mktemp(suffix=".mp4"))
        ani.save(str(temp_video_path), writer="ffmpeg", fps=fps, dpi=300)
        LOGGER.info(f"Temporary animation saved as '{temp_video_path}'")

        # Add music to the video
        video_duration = duration_sec + SUMMARY_FADE_IN_SEC
        selected_music = music_manager.select_music(music_filename)

        if music_manager.add_music_to_video(
            temp_video_path, Path(final_filename), selected_music, video_duration
        ):
            # Clean up temporary file
            temp_video_path.unlink()

            # Log music attribution
            attribution = music_manager.get_music_attribution(selected_music)
            if attribution:
                LOGGER.info(f"Music attribution: {attribution}")
        else:
            # If music integration failed, use the temporary file as final output
            temp_video_path.rename(final_filename)
            LOGGER.warning("Music integration failed, saved video without audio")
    else:
        # No music available, save directly
        ani.save(filename, writer="ffmpeg", fps=fps, dpi=300)
        LOGGER.info("No music available, saved video without audio")

    LOGGER.info(f"Animation successfully saved as '{final_filename}'")
    plt.close(fig)
